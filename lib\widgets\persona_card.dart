import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_modal.dart';

/// A reusable card widget for displaying SystemPersona information
///
/// This widget provides a vertical layout with:
/// - Centered persona avatar/image
/// - Persona name and description below the image
/// - Consistent theming and spacing
/// - Tap handling with visual feedback
/// - Support for selection states
/// - Optional video playback on avatar tap
class PersonaCard extends StatelessWidget {
  final models.SystemPersona persona;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onAvatarTap;
  final VoidCallback? onStartChat;
  final bool showSelectionIndicator;
  final bool showStartChatButton;
  final EdgeInsets? margin;
  final double? width;
  final double? height;

  const PersonaCard({
    super.key,
    required this.persona,
    this.isSelected = false,
    this.onTap,
    this.onAvatarTap,
    this.onStartChat,
    this.showSelectionIndicator = false,
    this.showStartChatButton = false,
    this.margin,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin ?? const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Card(
        elevation: AppDimensions.elevationS,
        child: InkWell(
          onTap: onTap,
          borderRadius: AppDimensions.borderRadiusM,
          child: Container(
            padding: const EdgeInsets.all(AppDimensions.spacingM),
            decoration: BoxDecoration(
              borderRadius: AppDimensions.borderRadiusM,
              border: isSelected
                  ? Border.all(color: context.colorScheme.primary, width: 2)
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Avatar section with tap handling
                _buildAvatar(context),

                SizedBox(height: AppDimensions.spacingM),

                // Name with selection indicator
                _buildNameWithIndicator(context),

                SizedBox(height: AppDimensions.spacingS),

                // Metadata section
                Flexible(child: _buildMetadata(context)),

                // Start Chat button if enabled
                if (showStartChatButton) ...[
                  SizedBox(height: AppDimensions.spacingM),
                  _buildStartChatButton(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return GestureDetector(
      onTap: onAvatarTap ?? () => _showVideoModal(context),
      child: AspectRatio(
        aspectRatio: 2 / 3, // 2:3 aspect ratio for 1024x1536 images
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: AppDimensions.borderRadiusL,
            boxShadow: [
              BoxShadow(
                color: context.colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: AppDimensions.borderRadiusL,
                  color: context.colorScheme.primaryContainer,
                  image: _getAvatarImage() != null
                      ? DecorationImage(
                          image: _getAvatarImage()!,
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: _getAvatarImage() == null
                    ? Icon(
                        AppIcons.profile,
                        size: 60,
                        color: context.colorScheme.onPrimaryContainer,
                      )
                    : null,
              ),
              // Play button overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: AppDimensions.borderRadiusL,
                    color: Colors.black.withValues(alpha: 0.3),
                  ),
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 40,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showVideoModal(BuildContext context) {
    PersonaVideoModal.show(
      context,
      persona: persona,
      videoPath: 'assets/persona-videos/ZenMasterVideo.mp4',
    );
  }

  Widget _buildNameWithIndicator(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            persona.name,
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (showSelectionIndicator && isSelected) ...[
          SizedBox(width: AppDimensions.spacingS),
          Icon(
            Icons.check_circle,
            color: context.colorScheme.primary,
            size: 20,
          ),
        ],
      ],
    );
  }

  Widget _buildMetadata(BuildContext context) {
    final metadata = persona.metadata;
    if (metadata == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Coaching Style
        if (metadata['coachingStyle'] != null) ...[
          _buildMetadataRow(
            context,
            'Style',
            metadata['coachingStyle'] as String,
          ),
          SizedBox(height: AppDimensions.spacingS),
        ],

        // Specialties as tags
        if (metadata['specialties'] != null) ...[
          _buildSpecialtiesTags(context, metadata['specialties'] as List),
          SizedBox(height: AppDimensions.spacingS),
        ],

        // Approach
        if (metadata['approach'] != null) ...[
          _buildMetadataRow(
            context,
            'Approach',
            metadata['approach'] as String,
          ),
        ],
      ],
    );
  }

  Widget _buildMetadataRow(BuildContext context, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ',
          style: context.textTheme.bodySmall?.copyWith(
            color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value.replaceAll('-', ' ').toLowerCase(),
            style: context.textTheme.bodySmall?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSpecialtiesTags(BuildContext context, List specialties) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Specialties:',
          style: context.textTheme.bodySmall?.copyWith(
            color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: AppDimensions.spacingXs),
        Wrap(
          spacing: AppDimensions.spacingXs,
          runSpacing: AppDimensions.spacingXs,
          children: specialties.map<Widget>((specialty) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.spacingS,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: context.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: context.colorScheme.primary.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
              child: Text(
                (specialty as String).replaceAll('-', ' ').toLowerCase(),
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colorScheme.onPrimaryContainer,
                  fontSize: 10,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStartChatButton(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: ElevatedButton(
        onPressed: onStartChat,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.colorScheme.primary,
          foregroundColor: context.colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacingM,
            vertical: AppDimensions.spacingS,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusM,
          ),
        ),
        child: const Text('Start Chat'),
      ),
    );
  }

  ImageProvider? _getAvatarImage() {
    if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
      return null;
    }

    if (persona.avatarUrl!.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl!);
    } else {
      return NetworkImage(persona.avatarUrl!);
    }
  }
}
